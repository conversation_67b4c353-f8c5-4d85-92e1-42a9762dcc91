package handlers

import (
	"LeadProcessing/models"
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockLeadService is a mock implementation of LeadService
type MockLeadService struct {
	mock.Mock
}

func (m *MockLeadService) ProcessLead(ctx context.Context, request *models.ProcessLeadRequest) (*models.ProcessLeadResponse, error) {
	args := m.Called(ctx, request)
	return args.Get(0).(*models.ProcessLeadResponse), args.Error(1)
}

func TestLeadHandler_HealthCheck(t *testing.T) {
	// Setup
	gin.SetMode(gin.TestMode)
	mockService := new(MockLeadService)
	logger := logrus.New()
	handler := NewLeadHandler(mockService, logger)

	// Create test router
	router := gin.New()
	router.GET("/health", handler.HealthCheck)

	// Create test request
	req, _ := http.NewRequest("GET", "/health", nil)
	w := httptest.NewRecorder()

	// Execute request
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.True(t, response["success"].(bool))
	assert.Equal(t, "Service is healthy", response["message"])
}

func TestLeadHandler_ProcessLead_Success(t *testing.T) {
	// Setup
	gin.SetMode(gin.TestMode)
	mockService := new(MockLeadService)
	logger := logrus.New()
	handler := NewLeadHandler(mockService, logger)

	// Mock service response
	expectedResponse := &models.ProcessLeadResponse{
		Success: true,
		Message: "Lead processed successfully",
		LeadID:  123,
		Status:  "processed",
	}
	mockService.On("ProcessLead", mock.Anything, mock.AnythingOfType("*models.ProcessLeadRequest")).Return(expectedResponse, nil)

	// Create test router
	router := gin.New()
	router.POST("/process", handler.ProcessLead)

	// Create test request
	requestBody := models.ProcessLeadRequest{
		LeadID:     123,
		AutoAccept: true,
	}
	jsonBody, _ := json.Marshal(requestBody)
	req, _ := http.NewRequest("POST", "/process", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	// Execute request
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.True(t, response["success"].(bool))
	assert.Equal(t, "Lead processed successfully", response["message"])

	// Verify mock was called
	mockService.AssertExpectations(t)
}

func TestLeadHandler_ProcessLead_InvalidRequest(t *testing.T) {
	// Setup
	gin.SetMode(gin.TestMode)
	mockService := new(MockLeadService)
	logger := logrus.New()
	handler := NewLeadHandler(mockService, logger)

	// Create test router
	router := gin.New()
	router.POST("/process", handler.ProcessLead)

	// Create invalid test request (missing required field)
	invalidBody := `{"auto_accept": true}`
	req, _ := http.NewRequest("POST", "/process", bytes.NewBuffer([]byte(invalidBody)))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	// Execute request
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusBadRequest, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.False(t, response["success"].(bool))
	assert.Equal(t, "Invalid request body", response["message"])
}
