package handlers

import (
	"LeadProcessing/models"
	"LeadProcessing/services"
	"LeadProcessing/utils"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

type LeadHandler struct {
	leadService services.LeadService
	logger      *logrus.Logger
}

func NewLeadHandler(leadService services.LeadService, logger *logrus.Logger) *LeadHandler {
	return &LeadHandler{
		leadService: leadService,
		logger:      logger,
	}
}

// ProcessLead handles the lead processing request
// @Summary Process a lead
// @Description Process a lead through the 12-step workflow
// @Tags leads
// @Accept json
// @Produce json
// @Param request body models.ProcessLeadRequest true "Process Lead Request"
// @Success 200 {object} models.ProcessLeadResponse
// @Failure 400 {object} utils.ErrorResponse
// @Failure 500 {object} utils.ErrorResponse
// @Router /api/v1/leads/process [post]
func (h *LeadHandler) ProcessLead(c *gin.Context) {
	var request models.ProcessLeadRequest
	
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.WithError(err).Error("Invalid request body")
		utils.SendErrorResponse(c, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	h.logger.WithFields(logrus.Fields{
		"lead_id":     request.LeadID,
		"auto_accept": request.AutoAccept,
	}).Info("Processing lead request")

	response, err := h.leadService.ProcessLead(c.Request.Context(), &request)
	if err != nil {
		h.logger.WithError(err).Error("Failed to process lead")
		utils.SendErrorResponse(c, http.StatusInternalServerError, "Failed to process lead", err.Error())
		return
	}

	h.logger.WithFields(logrus.Fields{
		"lead_id": response.LeadID,
		"status":  response.Status,
	}).Info("Lead processed successfully")

	utils.SuccessResponse(c, http.StatusOK, "Lead processed successfully", response)
}

// GetLead retrieves a lead by ID
// @Summary Get a lead
// @Description Get a lead by its ID
// @Tags leads
// @Produce json
// @Param id path int true "Lead ID"
// @Success 200 {object} models.Lead
// @Failure 400 {object} utils.ErrorResponse
// @Failure 404 {object} utils.ErrorResponse
// @Failure 500 {object} utils.ErrorResponse
// @Router /api/v1/leads/{id} [get]
func (h *LeadHandler) GetLead(c *gin.Context) {
	leadIDStr := c.Param("id")
	leadID, err := strconv.ParseUint(leadIDStr, 10, 32)
	if err != nil {
		h.logger.WithError(err).Error("Invalid lead ID")
		utils.SendErrorResponse(c, http.StatusBadRequest, "Invalid lead ID", err.Error())
		return
	}

	h.logger.WithField("lead_id", leadID).Info("Fetching lead")

	// This would typically use a different service method to just fetch the lead
	// For now, we'll create a simple request to demonstrate
	request := &models.ProcessLeadRequest{
		LeadID: uint(leadID),
	}

	// Note: In a real implementation, you'd have a separate GetLead method
	// that doesn't process the lead, just retrieves it
	response, err := h.leadService.ProcessLead(c.Request.Context(), request)
	if err != nil {
		h.logger.WithError(err).Error("Failed to fetch lead")
		utils.SendErrorResponse(c, http.StatusInternalServerError, "Failed to fetch lead", err.Error())
		return
	}

	utils.SuccessResponse(c, http.StatusOK, "Lead retrieved successfully", response)
}

// HealthCheck provides a health check endpoint
// @Summary Health check
// @Description Check if the service is healthy
// @Tags health
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /health [get]
func (h *LeadHandler) HealthCheck(c *gin.Context) {
	utils.SuccessResponse(c, http.StatusOK, "Service is healthy", map[string]interface{}{
		"status":    "healthy",
		"service":   "lead-processing",
		"timestamp": "2024-01-01T00:00:00Z", // You can use time.Now() here
	})
}
