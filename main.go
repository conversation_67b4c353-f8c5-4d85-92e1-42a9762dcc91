package main

import (
	"LeadProcessing/config"
	"LeadProcessing/handlers"
	"LeadProcessing/middleware"
	"LeadProcessing/models"
	"LeadProcessing/repositories"
	"LeadProcessing/services"
	"LeadProcessing/websocket"
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func main() {
	// Load configuration
	cfg := config.LoadConfig()

	// Initialize logger
	logger := logrus.New()
	logger.SetFormatter(&logrus.JSONFormatter{})
	logger.SetLevel(logrus.InfoLevel)

	logger.Info("Starting Lead Processing Service")

	// Initialize database
	db, err := initDatabase(cfg, logger)
	if err != nil {
		logger.WithError(err).Fatal("Failed to initialize database")
	}

	// Initialize Redis
	redisClient := initRedis(cfg, logger)

	// Initialize WebSocket hub
	wsHub := websocket.NewHub()
	go wsHub.Run()

	// Initialize repositories
	leadRepo := repositories.NewLeadRepository(db)

	// Initialize services
	leadService := services.NewLeadService(
		leadRepo,
		redisClient,
		logger,
		wsHub,
		"http://external-api.example.com/availability", // Configure this URL
	)

	// Initialize handlers
	leadHandler := handlers.NewLeadHandler(leadService, logger)

	// Initialize Gin router
	router := setupRouter(leadHandler, wsHub, logger)

	// Start server
	srv := &http.Server{
		Addr:    fmt.Sprintf("%s:%s", cfg.Server.Host, cfg.Server.Port),
		Handler: router,
	}

	// Start server in a goroutine
	go func() {
		logger.WithField("address", srv.Addr).Info("Starting HTTP server")
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.WithError(err).Fatal("Failed to start server")
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down server...")

	// Give outstanding requests 30 seconds to complete
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		logger.WithError(err).Fatal("Server forced to shutdown")
	}

	logger.Info("Server exited")
}

func initDatabase(cfg *config.Config, logger *logrus.Logger) (*gorm.DB, error) {
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=%s",
		cfg.Database.Host,
		cfg.Database.User,
		cfg.Database.Password,
		cfg.Database.DBName,
		cfg.Database.Port,
		cfg.Database.SSLMode,
	)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Auto-migrate the schema
	err = db.AutoMigrate(&models.Lead{}, &models.Activity{}, &models.TradeDrug{})
	if err != nil {
		return nil, fmt.Errorf("failed to migrate database: %w", err)
	}

	logger.Info("Database initialized successfully")
	return db, nil
}

func initRedis(cfg *config.Config, logger *logrus.Logger) *redis.Client {
	rdb := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%s", cfg.Redis.Host, cfg.Redis.Port),
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
	})

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		logger.WithError(err).Warn("Failed to connect to Redis, continuing without cache")
	} else {
		logger.Info("Redis initialized successfully")
	}

	return rdb
}

func setupRouter(leadHandler *handlers.LeadHandler, wsHub *websocket.Hub, logger *logrus.Logger) *gin.Engine {
	// Set Gin mode
	gin.SetMode(gin.ReleaseMode)

	router := gin.New()

	// Add middleware
	router.Use(middleware.CORSMiddleware())
	router.Use(middleware.RequestIDMiddleware())
	router.Use(middleware.LoggingMiddleware(logger))
	router.Use(gin.Recovery())

	// Health check endpoint
	router.GET("/health", leadHandler.HealthCheck)

	// WebSocket endpoint
	router.GET("/ws", wsHub.HandleWebSocket)

	// API routes
	v1 := router.Group("/api/v1")
	{
		leads := v1.Group("/leads")
		{
			leads.POST("/process", leadHandler.ProcessLead)
			leads.GET("/:id", leadHandler.GetLead)
		}
	}

	return router
}