# Lead Processing Service

A Go-based microservice for processing pharmaceutical leads with Redis caching and PostgreSQL database.

## Features

- **12-Step Lead Processing Workflow**
- **RESTful API** with Gin framework
- **PostgreSQL Database** with GORM ORM
- **Redis Caching** for performance optimization
- **WebSocket Support** for real-time updates
- **Structured Logging** with Logrus
- **Graceful Shutdown** handling
- **CORS Support** for web clients

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   HTTP Client   │    │   WebSocket     │    │  External API   │
│                 │    │   Client        │    │                 │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          ▼                      ▼                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                     Gin HTTP Server                            │
├─────────────────────────────────────────────────────────────────┤
│  Handlers  │  Middleware  │  WebSocket Hub  │  Services        │
├─────────────────────────────────────────────────────────────────┤
│              Repositories (Data Access Layer)                  │
├─────────────────────────────────────────────────────────────────┤
│  PostgreSQL Database  │  Redis Cache  │  External Services     │
└─────────────────────────────────────────────────────────────────┘
```

## 12-Step Lead Processing Workflow

1. **Log Start** - Initialize processing with structured logging
2. **Fetch Lead and Activities** - Retrieve data from PostgreSQL
3. **Prepare Activities and Selected Maps** - Create processing maps
4. **Flatten Trade Drugs** - Process drug data structure
5. **Set All Activities to Unavailable** - Reset availability status
6. **Prepare Products List** - Format data for external API
7. **Call External Service** - Check product availability
8. **Update Trade Drugs and Activities** - Apply availability results
9. **Commit DB Changes** - Persist updates to database
10. **Auto-Accept if Needed** - Apply business rules
11. **Send WebSocket Message** - Notify connected clients
12. **Return Response** - Send result to client

## API Endpoints

### Process Lead
```http
POST /api/v1/leads/process
Content-Type: application/json

{
  "lead_id": 123,
  "auto_accept": true,
  "options": {
    "priority": "high"
  }
}
```

### Get Lead
```http
GET /api/v1/leads/{id}
```

### Health Check
```http
GET /health
```

### WebSocket Connection
```
ws://localhost:8080/ws
```

## Setup and Installation

### Prerequisites
- Go 1.21+
- PostgreSQL 12+
- Redis 6+

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd LeadProcessing
```

2. **Install dependencies**
```bash
go mod tidy
```

3. **Setup environment variables**
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Setup PostgreSQL database**
```sql
CREATE DATABASE lead_processing;
```

5. **Start Redis server**
```bash
redis-server
```

6. **Run the application**
```bash
go run main.go
```

The service will start on `http://localhost:8080`

## Configuration

Environment variables can be set in `.env` file or as system environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_HOST` | PostgreSQL host | localhost |
| `DB_PORT` | PostgreSQL port | 5432 |
| `DB_USER` | Database user | postgres |
| `DB_PASSWORD` | Database password | - |
| `DB_NAME` | Database name | lead_processing |
| `REDIS_HOST` | Redis host | localhost |
| `REDIS_PORT` | Redis port | 6379 |
| `SERVER_PORT` | HTTP server port | 8080 |

## Database Schema

### Leads Table
- `id` (Primary Key)
- `customer_id`
- `status`
- `priority`
- `source`
- `notes`
- `processed_at`
- `auto_accepted`
- `created_at`, `updated_at`, `deleted_at`

### Activities Table
- `id` (Primary Key)
- `lead_id` (Foreign Key)
- `type`
- `description`
- `status`
- `available`
- `quantity`
- `price`
- `created_at`, `updated_at`, `deleted_at`

### Trade Drugs Table
- `id` (Primary Key)
- `lead_id` (Foreign Key)
- `drug_name`
- `drug_code`
- `quantity`
- `price`
- `available`
- `external_id`
- `processed_data` (JSONB)
- `created_at`, `updated_at`, `deleted_at`

## Testing

```bash
# Run tests
go test ./...

# Run tests with coverage
go test -cover ./...

# Run specific test
go test ./services -v
```

## Docker Support

```dockerfile
# Dockerfile example
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download
COPY . .
RUN go build -o main .

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/main .
CMD ["./main"]
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
