package models

import (
	"time"

	"gorm.io/gorm"
)

// Lead represents the main lead entity
type Lead struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
	
	// Lead specific fields
	CustomerID    uint      `json:"customer_id" gorm:"not null"`
	Status        string    `json:"status" gorm:"default:'pending'"`
	Priority      int       `json:"priority" gorm:"default:1"`
	Source        string    `json:"source"`
	Notes         string    `json:"notes"`
	ProcessedAt   *time.Time `json:"processed_at,omitempty"`
	AutoAccepted  bool      `json:"auto_accepted" gorm:"default:false"`
	
	// Relationships
	Activities  []Activity  `json:"activities" gorm:"foreignKey:LeadID"`
	TradeDrugs  []TradeDrug `json:"trade_drugs" gorm:"foreignKey:LeadID"`
}

// Activity represents lead activities
type Activity struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
	
	LeadID      uint   `json:"lead_id" gorm:"not null"`
	Type        string `json:"type" gorm:"not null"`
	Description string `json:"description"`
	Status      string `json:"status" gorm:"default:'available'"`
	Available   bool   `json:"available" gorm:"default:true"`
	Quantity    int    `json:"quantity" gorm:"default:1"`
	Price       float64 `json:"price"`
	
	// Foreign key
	Lead *Lead `json:"lead,omitempty" gorm:"foreignKey:LeadID"`
}

// TradeDrug represents trade drug information
type TradeDrug struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
	
	LeadID      uint    `json:"lead_id" gorm:"not null"`
	DrugName    string  `json:"drug_name" gorm:"not null"`
	DrugCode    string  `json:"drug_code"`
	Quantity    int     `json:"quantity" gorm:"default:1"`
	Price       float64 `json:"price"`
	Available   bool    `json:"available" gorm:"default:true"`
	ExternalID  string  `json:"external_id"`
	
	// Flattened fields for processing
	ProcessedData map[string]interface{} `json:"processed_data" gorm:"type:jsonb"`
	
	// Foreign key
	Lead *Lead `json:"lead,omitempty" gorm:"foreignKey:LeadID"`
}

// ProcessLeadRequest represents the request structure for processing leads
type ProcessLeadRequest struct {
	LeadID      uint                   `json:"lead_id" binding:"required"`
	Options     map[string]interface{} `json:"options,omitempty"`
	AutoAccept  bool                   `json:"auto_accept,omitempty"`
}

// ProcessLeadResponse represents the response structure
type ProcessLeadResponse struct {
	Success     bool                   `json:"success"`
	Message     string                 `json:"message"`
	LeadID      uint                   `json:"lead_id"`
	Status      string                 `json:"status"`
	ProcessedAt time.Time              `json:"processed_at"`
	Data        map[string]interface{} `json:"data,omitempty"`
}

// ExternalServiceRequest represents request to external availability service
type ExternalServiceRequest struct {
	Products []ProductRequest `json:"products"`
	LeadID   uint            `json:"lead_id"`
}

// ProductRequest represents individual product in external service request
type ProductRequest struct {
	ID       uint   `json:"id"`
	DrugCode string `json:"drug_code"`
	DrugName string `json:"drug_name"`
	Quantity int    `json:"quantity"`
}

// ExternalServiceResponse represents response from external availability service
type ExternalServiceResponse struct {
	Success  bool                    `json:"success"`
	Products []ProductAvailability   `json:"products"`
	Message  string                  `json:"message,omitempty"`
}

// ProductAvailability represents availability info for a product
type ProductAvailability struct {
	ID        uint    `json:"id"`
	Available bool    `json:"available"`
	Price     float64 `json:"price,omitempty"`
	Quantity  int     `json:"quantity,omitempty"`
	Message   string  `json:"message,omitempty"`
}

// WebSocketMessage represents WebSocket message structure
type WebSocketMessage struct {
	Type    string                 `json:"type"`
	LeadID  uint                   `json:"lead_id"`
	Status  string                 `json:"status"`
	Data    map[string]interface{} `json:"data,omitempty"`
	Timestamp time.Time            `json:"timestamp"`
}
