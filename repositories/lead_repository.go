package repositories

import (
	"LeadProcessing/models"
	"context"
	"fmt"

	"gorm.io/gorm"
)

type LeadRepository interface {
	GetLeadWithActivities(ctx context.Context, leadID uint) (*models.Lead, error)
	UpdateLead(ctx context.Context, lead *models.Lead) error
	UpdateActivities(ctx context.Context, activities []models.Activity) error
	UpdateTradeDrugs(ctx context.Context, tradeDrugs []models.TradeDrug) error
	BeginTransaction() *gorm.DB
	CommitTransaction(tx *gorm.DB) error
	RollbackTransaction(tx *gorm.DB) error
}

type leadRepository struct {
	db *gorm.DB
}

func NewLeadRepository(db *gorm.DB) LeadRepository {
	return &leadRepository{db: db}
}

func (r *leadRepository) GetLeadWithActivities(ctx context.Context, leadID uint) (*models.Lead, error) {
	var lead models.Lead
	
	err := r.db.WithContext(ctx).
		Preload("Activities").
		Preload("TradeDrugs").
		First(&lead, leadID).Error
	
	if err != nil {
		return nil, fmt.Errorf("failed to fetch lead with ID %d: %w", leadID, err)
	}
	
	return &lead, nil
}

func (r *leadRepository) UpdateLead(ctx context.Context, lead *models.Lead) error {
	err := r.db.WithContext(ctx).Save(lead).Error
	if err != nil {
		return fmt.Errorf("failed to update lead: %w", err)
	}
	return nil
}

func (r *leadRepository) UpdateActivities(ctx context.Context, activities []models.Activity) error {
	if len(activities) == 0 {
		return nil
	}
	
	for _, activity := range activities {
		err := r.db.WithContext(ctx).Save(&activity).Error
		if err != nil {
			return fmt.Errorf("failed to update activity %d: %w", activity.ID, err)
		}
	}
	
	return nil
}

func (r *leadRepository) UpdateTradeDrugs(ctx context.Context, tradeDrugs []models.TradeDrug) error {
	if len(tradeDrugs) == 0 {
		return nil
	}
	
	for _, tradeDrug := range tradeDrugs {
		err := r.db.WithContext(ctx).Save(&tradeDrug).Error
		if err != nil {
			return fmt.Errorf("failed to update trade drug %d: %w", tradeDrug.ID, err)
		}
	}
	
	return nil
}

func (r *leadRepository) BeginTransaction() *gorm.DB {
	return r.db.Begin()
}

func (r *leadRepository) CommitTransaction(tx *gorm.DB) error {
	return tx.Commit().Error
}

func (r *leadRepository) RollbackTransaction(tx *gorm.DB) error {
	return tx.Rollback().Error
}
