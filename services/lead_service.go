package services

import (
	"LeadProcessing/models"
	"LeadProcessing/repositories"
	"LeadProcessing/websocket"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
)

type LeadService interface {
	ProcessLead(ctx context.Context, request *models.ProcessLeadRequest) (*models.ProcessLeadResponse, error)
}

type leadService struct {
	leadRepo      repositories.LeadRepository
	redisClient   *redis.Client
	logger        *logrus.Logger
	wsHub         *websocket.Hub
	externalAPIURL string
}

func NewLeadService(
	leadRepo repositories.LeadRepository,
	redisClient *redis.Client,
	logger *logrus.Logger,
	wsHub *websocket.Hub,
	externalAPIURL string,
) LeadService {
	return &leadService{
		leadRepo:       leadRepo,
		redisClient:    redisClient,
		logger:         logger,
		wsHub:          wsHub,
		externalAPIURL: externalAPIURL,
	}
}

func (s *leadService) ProcessLead(ctx context.Context, request *models.ProcessLeadRequest) (*models.ProcessLeadResponse, error) {
	// Step 1: Log start
	s.logger.WithFields(logrus.Fields{
		"lead_id": request.LeadID,
		"step":    1,
	}).Info("Starting lead processing")

	// Step 2: Fetch lead and activities from DB
	lead, err := s.leadRepo.GetLeadWithActivities(ctx, request.LeadID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to fetch lead from database")
		return nil, fmt.Errorf("failed to fetch lead: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"lead_id":        lead.ID,
		"activities_count": len(lead.Activities),
		"trade_drugs_count": len(lead.TradeDrugs),
		"step":           2,
	}).Info("Fetched lead and activities from DB")

	// Step 3: Prepare activities and selected maps
	activitiesMap := make(map[uint]*models.Activity)
	selectedActivities := make(map[uint]bool)
	
	for i := range lead.Activities {
		activity := &lead.Activities[i]
		activitiesMap[activity.ID] = activity
		selectedActivities[activity.ID] = activity.Available
	}

	s.logger.WithFields(logrus.Fields{
		"lead_id": lead.ID,
		"step":    3,
	}).Info("Prepared activities and selected maps")

	// Step 4: Flatten trade drugs
	flattenedTradeDrugs := s.flattenTradeDrugs(lead.TradeDrugs)
	
	s.logger.WithFields(logrus.Fields{
		"lead_id":                lead.ID,
		"flattened_drugs_count": len(flattenedTradeDrugs),
		"step":                   4,
	}).Info("Flattened trade drugs")

	// Step 5: Set all activities to unavailable
	for _, activity := range lead.Activities {
		activity.Available = false
		activity.Status = "unavailable"
	}

	s.logger.WithFields(logrus.Fields{
		"lead_id": lead.ID,
		"step":    5,
	}).Info("Set all activities to unavailable")

	// Step 6: Prepare products list
	products := s.prepareProductsList(flattenedTradeDrugs)
	
	s.logger.WithFields(logrus.Fields{
		"lead_id":       lead.ID,
		"products_count": len(products),
		"step":          6,
	}).Info("Prepared products list")

	// Step 7: Call external service for availability
	availabilityResponse, err := s.callExternalAvailabilityService(ctx, &models.ExternalServiceRequest{
		Products: products,
		LeadID:   lead.ID,
	})
	if err != nil {
		s.logger.WithError(err).Error("Failed to call external availability service")
		return nil, fmt.Errorf("failed to check availability: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"lead_id": lead.ID,
		"step":    7,
	}).Info("Called external service for availability")

	// Step 8: Update trade drugs and activities
	err = s.updateTradeDrugsAndActivities(lead, availabilityResponse, activitiesMap)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update trade drugs and activities")
		return nil, fmt.Errorf("failed to update data: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"lead_id": lead.ID,
		"step":    8,
	}).Info("Updated trade drugs and activities")

	// Step 9: Commit DB changes
	err = s.commitDatabaseChanges(ctx, lead)
	if err != nil {
		s.logger.WithError(err).Error("Failed to commit database changes")
		return nil, fmt.Errorf("failed to commit changes: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"lead_id": lead.ID,
		"step":    9,
	}).Info("Committed DB changes")

	// Step 10: Auto-accept if needed
	if request.AutoAccept {
		lead.AutoAccepted = true
		lead.Status = "accepted"
		err = s.leadRepo.UpdateLead(ctx, lead)
		if err != nil {
			s.logger.WithError(err).Error("Failed to auto-accept lead")
		}
	}

	s.logger.WithFields(logrus.Fields{
		"lead_id":     lead.ID,
		"auto_accept": request.AutoAccept,
		"step":        10,
	}).Info("Auto-accept processing completed")

	// Step 11: Send WebSocket message
	s.sendWebSocketMessage(lead)

	s.logger.WithFields(logrus.Fields{
		"lead_id": lead.ID,
		"step":    11,
	}).Info("Sent WebSocket message")

	// Step 12: Return response
	response := &models.ProcessLeadResponse{
		Success:     true,
		Message:     "Lead processed successfully",
		LeadID:      lead.ID,
		Status:      lead.Status,
		ProcessedAt: time.Now(),
		Data: map[string]interface{}{
			"activities_count":   len(lead.Activities),
			"trade_drugs_count": len(lead.TradeDrugs),
			"auto_accepted":     lead.AutoAccepted,
		},
	}

	s.logger.WithFields(logrus.Fields{
		"lead_id": lead.ID,
		"step":    12,
	}).Info("Lead processing completed successfully")

	return response, nil
}

// Helper methods

func (s *leadService) flattenTradeDrugs(tradeDrugs []models.TradeDrug) []models.TradeDrug {
	// Implement flattening logic based on your business requirements
	// This is a placeholder implementation
	flattened := make([]models.TradeDrug, 0, len(tradeDrugs))

	for _, drug := range tradeDrugs {
		// Add flattening logic here
		// For now, just copy the drugs
		flattened = append(flattened, drug)
	}

	return flattened
}

func (s *leadService) prepareProductsList(tradeDrugs []models.TradeDrug) []models.ProductRequest {
	products := make([]models.ProductRequest, 0, len(tradeDrugs))

	for _, drug := range tradeDrugs {
		products = append(products, models.ProductRequest{
			ID:       drug.ID,
			DrugCode: drug.DrugCode,
			DrugName: drug.DrugName,
			Quantity: drug.Quantity,
		})
	}

	return products
}

func (s *leadService) callExternalAvailabilityService(ctx context.Context, request *models.ExternalServiceRequest) (*models.ExternalServiceResponse, error) {
	// Prepare request body
	requestBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	httpReq, err := http.NewRequestWithContext(ctx, "POST", s.externalAPIURL, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")

	// Make HTTP call
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to make HTTP request: %w", err)
	}
	defer resp.Body.Close()

	// Parse response
	var response models.ExternalServiceResponse
	err = json.NewDecoder(resp.Body).Decode(&response)
	if err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &response, nil
}

func (s *leadService) updateTradeDrugsAndActivities(lead *models.Lead, availabilityResponse *models.ExternalServiceResponse, activitiesMap map[uint]*models.Activity) error {
	// Create a map for quick lookup of availability data
	availabilityMap := make(map[uint]models.ProductAvailability)
	for _, product := range availabilityResponse.Products {
		availabilityMap[product.ID] = product
	}

	// Update trade drugs
	for i := range lead.TradeDrugs {
		drug := &lead.TradeDrugs[i]
		if availability, exists := availabilityMap[drug.ID]; exists {
			drug.Available = availability.Available
			if availability.Price > 0 {
				drug.Price = availability.Price
			}
			if availability.Quantity > 0 {
				drug.Quantity = availability.Quantity
			}
		}
	}

	// Update activities based on trade drug availability
	for i := range lead.Activities {
		activity := &lead.Activities[i]
		// Logic to determine activity availability based on related trade drugs
		// This is a simplified implementation
		activity.Available = true
		activity.Status = "available"

		// You can add more complex logic here based on your business rules
	}

	return nil
}

func (s *leadService) commitDatabaseChanges(ctx context.Context, lead *models.Lead) error {
	// Update lead
	err := s.leadRepo.UpdateLead(ctx, lead)
	if err != nil {
		return fmt.Errorf("failed to update lead: %w", err)
	}

	// Update activities
	err = s.leadRepo.UpdateActivities(ctx, lead.Activities)
	if err != nil {
		return fmt.Errorf("failed to update activities: %w", err)
	}

	// Update trade drugs
	err = s.leadRepo.UpdateTradeDrugs(ctx, lead.TradeDrugs)
	if err != nil {
		return fmt.Errorf("failed to update trade drugs: %w", err)
	}

	// Update processed timestamp
	now := time.Now()
	lead.ProcessedAt = &now
	lead.Status = "processed"

	return s.leadRepo.UpdateLead(ctx, lead)
}

func (s *leadService) sendWebSocketMessage(lead *models.Lead) {
	message := models.WebSocketMessage{
		Type:   "lead_processed",
		LeadID: lead.ID,
		Status: lead.Status,
		Data: map[string]interface{}{
			"processed_at":      lead.ProcessedAt,
			"auto_accepted":     lead.AutoAccepted,
			"activities_count":  len(lead.Activities),
			"trade_drugs_count": len(lead.TradeDrugs),
		},
		Timestamp: time.Now(),
	}

	// Send message through WebSocket hub
	if s.wsHub != nil {
		s.wsHub.Broadcast <- message
	}
}
