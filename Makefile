# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
BINARY_NAME=lead-processing
BINARY_UNIX=$(BINARY_NAME)_unix

# Docker parameters
DOCKER_IMAGE=lead-processing
DOCKER_TAG=latest

.PHONY: all build clean test coverage deps run docker-build docker-run docker-compose-up docker-compose-down help

all: test build

## Build the binary
build:
	$(GOBUILD) -o $(BINARY_NAME) -v .

## Clean build files
clean:
	$(GOCLEAN)
	rm -f $(BINARY_NAME)
	rm -f $(BINARY_UNIX)

## Run tests
test:
	$(GOTEST) -v ./...

## Run tests with coverage
coverage:
	$(GOTEST) -cover ./...

## Run tests with coverage report
coverage-html:
	$(GOTEST) -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out

## Download dependencies
deps:
	$(GOMOD) download
	$(GOMOD) tidy

## Run the application
run:
	$(GOCMD) run main.go

## Build for Linux
build-linux:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) -o $(BINARY_UNIX) -v

## Build Docker image
docker-build:
	docker build -t $(DOCKER_IMAGE):$(DOCKER_TAG) .

## Run Docker container
docker-run:
	docker run -p 8080:8080 $(DOCKER_IMAGE):$(DOCKER_TAG)

## Start services with docker-compose
docker-compose-up:
	docker-compose up -d

## Stop services with docker-compose
docker-compose-down:
	docker-compose down

## View logs from docker-compose
docker-compose-logs:
	docker-compose logs -f

## Format code
fmt:
	$(GOCMD) fmt ./...

## Lint code
lint:
	golangci-lint run

## Install development tools
install-tools:
	$(GOGET) -u github.com/golangci/golangci-lint/cmd/golangci-lint

## Create .env file from example
env:
	cp .env.example .env

## Database migration (placeholder)
migrate:
	@echo "Database migration would go here"

## Show help
help:
	@echo ''
	@echo 'Usage:'
	@echo '  make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} { \
		if (/^[a-zA-Z_-]+:.*?##.*$$/) printf "  %-20s%s\n", $$1, $$2 \
	}' $(MAKEFILE_LIST)
